import os
import json
import re
from jira import JIRA
from collections import defaultdict
import openpyxl # Added for Excel reading

CACHE_FILE = "jira_issues_cache.json"
EXCEL_FILE_PATH = r"C:\Users\<USER>\Downloads\Softwareprogrammstatus Y2020.xlsm"
EXCEL_SHEET_NAME = "Funktionstatus"
EXCEL_COLUMN = 'B'
EXCEL_START_ROW = 7
EXCEL_END_ROW = 472

def get_jira_issues(issue_types, force_fetch=False):
    """
    Fetches Jira issues of specified types from project "RBKBK70",
    using a local cache file. It always attempts to establish a Jira connection.
    If force_fetch is True, it fetches from <PERSON>ra and overwrites the cache.
    Otherwise, if the cache file exists, it loads issues from there.
    If cache file doesn't exist, it fetches from <PERSON>ra with pagination and saves to the cache.
    """
    cache_key = "_".join(sorted(issue_types)).replace(" ", "_").replace("-", "_")
    current_cache_file = f"jira_issues_cache_{cache_key}.json"

    jira_base_url = os.getenv('JIRA_RBK_BASE_URL')
    jira_username = os.getenv('JIRA_USER')
    jira_api_token = os.getenv('JIRA_TOKEN')

    if not all([jira_base_url, jira_username, jira_api_token]):
        print("Error: Jira environment variables (JIRA_RBK_BASE_URL, JIRA_USER, JIRA_TOKEN) are not set.")
        return [], None # Return empty list and None for jira connection

    jira_connection = None
    try:
        jira_connection = JIRA(
            server=jira_base_url,
            basic_auth=(jira_username, jira_api_token)
        )
        print("Jira connection established.")

        cached_issues_data = []
        if os.path.exists(current_cache_file) and not force_fetch:
            print(f"Loading issues from cache file: {current_cache_file}")
            with open(current_cache_file, 'r', encoding='utf-8') as f:
                cached_issues_data = json.load(f)
            print(f"Found {len(cached_issues_data)} issues in cache.")
            return cached_issues_data, jira_connection
        
        print(f"Cache file not found or force_fetch is True for {issue_types}. Fetching issues from Jira...")
        jql_query = f'project = "RBKBK70" AND issuetype IN ({",".join(f'"{it}"' for it in issue_types)})'
        all_issues = []
        start_at = 0
        page_size = 100  # Jira's default maxResults is often 50 or 100, setting to 100 for efficiency

        while True:
            issues_page = jira_connection.search_issues(jql_query, startAt=start_at, maxResults=page_size)
            if not issues_page:
                break
            all_issues.extend(issues_page)
            start_at += len(issues_page)
            if len(issues_page) < page_size:
                break

        print(f"Found {len(all_issues)} issues of types {issue_types}:")
        
        # Prepare issues for caching (serialize relevant fields)
        issues_to_cache = []
        for issue in all_issues:
            issue_data = {
                "key": issue.key,
                "summary": issue.fields.summary,
                "issuetype": issue.fields.issuetype.name,
                "issuelinks": [] # Initialize issuelinks
            }
            if hasattr(issue.fields, 'issuelinks'):
                for link in issue.fields.issuelinks:
                    linked_issue_data = {}
                    if hasattr(link, 'inwardIssue'):
                        linked_issue_data['type'] = link.type.name
                        linked_issue_data['direction'] = 'inward'
                        linked_issue_data['key'] = link.inwardIssue.key
                        linked_issue_data['summary'] = link.inwardIssue.fields.summary
                        linked_issue_data['issuetype'] = link.inwardIssue.fields.issuetype.name
                    elif hasattr(link, 'outwardIssue'):
                        linked_issue_data['type'] = link.type.name
                        linked_issue_data['direction'] = 'outward'
                        linked_issue_data['key'] = link.outwardIssue.key
                        linked_issue_data['summary'] = link.outwardIssue.fields.summary
                        linked_issue_data['issuetype'] = link.outwardIssue.fields.issuetype.name
                    if linked_issue_data:
                        issue_data["issuelinks"].append(linked_issue_data)
            issues_to_cache.append(issue_data)
            # Debugging: Print issuelinks for a few issues to verify data
            if len(issues_to_cache) < 5: # Print for first 5 issues
                print(f"DEBUG: Issue {issue.key} issuelinks: {issue_data.get('issuelinks', 'N/A')}")
        
        # Save to cache file
        with open(current_cache_file, 'w', encoding='utf-8') as f:
            json.dump(issues_to_cache, f, ensure_ascii=False, indent=4)
        print(f"Issues saved to cache file: {current_cache_file}")
        
        return issues_to_cache, jira_connection # Return jira connection as well

    except Exception as e:
        print(f"An error occurred during Jira fetching or connection: {e}")
        return [], None # Return empty list and None for jira connection on error

def create_jira_issue_link(jira_connection, inward_key, outward_key, link_type="Relates"):
    """
    Creates a link between two Jira issues.
    """
    try:
        jira_connection.create_issue_link(
            type=link_type,
            inwardIssue=inward_key,
            outwardIssue=outward_key
        )
        print(f"Successfully linked {inward_key} to {outward_key} with type '{link_type}'.")
    except Exception as e:
        print(f"Error creating link between {inward_key} and {outward_key}: {e}")

def group_and_count_requirements(issues):
    """
    Groups requirements by security function (FXYYY or SFXYYY) and counts them.
    Requirements not matching the syntax are grouped under "unknown".
    """
    grouped_requirements = defaultdict(lambda: {"issues": [], "sub_components": defaultdict(list)})
    # Regex to find F or SF followed by one or more digits (the security function number)
    # It also handles cases where the pattern might be preceded by "RBKBK70-XXXX: "
    pattern = re.compile(r'(?:RBKBK70-\d+:\s*)?(?:S?F)(\d+)') 

    for issue in issues:
        summary = issue.get('summary', '')
        match = pattern.search(summary)
        
        if match:
            security_function_full_number = match.group(1)
            main_security_function_id = ""
            component_id = ""

            if len(security_function_full_number) == 3:
                main_security_function_id = security_function_full_number[0]
                component_id = security_function_full_number[1:]
            elif len(security_function_full_number) == 4:
                main_security_function_id = security_function_full_number[:2]
                component_id = security_function_full_number[2:]
            else:
                # Handle cases that don't match 3 or 4 digits, or put them to unknown
                grouped_requirements["unknown"]["issues"].append(issue)
                continue
            
            main_security_function = f"F{main_security_function_id}"
            
            grouped_requirements[main_security_function]["issues"].append(issue)
            grouped_requirements[main_security_function]["sub_components"][component_id].append(issue)
        else:
            grouped_requirements["unknown"]["issues"].append(issue)
    
    print("\n--- Requirement Grouping and Counts ---")
    for sf, data in sorted(grouped_requirements.items()):
        reqs = data["issues"]
        sub_components_dict = data["sub_components"]
        
        print(f"Security Function {sf}: {len(reqs)} requirements")
        if sf != "unknown":
            sorted_sub_components_keys = sorted(sub_components_dict.keys())
            sub_component_strings = []
            for sub_comp_key in sorted_sub_components_keys:
                sub_component_strings.append(f"{sub_comp_key} ({len(sub_components_dict[sub_comp_key])})")
            print(f"  Sub-components: {', '.join(sub_component_strings)}")

def get_excel_security_functions():
    """
    Reads security function names from a specified Excel file and sheet,
    and groups them by main security function and sub-components.
    """
    grouped_excel_functions = defaultdict(lambda: {"issues": [], "sub_components": defaultdict(list)})
    # Regex to find F or SF followed by one or more digits (the security function number)
    pattern = re.compile(r'(?:S?F)(\d+)') 

    try:
        if not os.path.exists(EXCEL_FILE_PATH):
            print(f"Error: Excel file not found at {EXCEL_FILE_PATH}")
            return grouped_excel_functions

        workbook = openpyxl.load_workbook(EXCEL_FILE_PATH, data_only=True)
        if EXCEL_SHEET_NAME not in workbook.sheetnames:
            print(f"Error: Sheet '{EXCEL_SHEET_NAME}' not found in the Excel file.")
            return grouped_excel_functions
        
        sheet = workbook[EXCEL_SHEET_NAME]

        for row_num in range(EXCEL_START_ROW, EXCEL_END_ROW + 1):
            cell_value = sheet[f"{EXCEL_COLUMN}{row_num}"].value
            if cell_value:
                function_name = str(cell_value).strip()
                match = pattern.search(function_name)
                
                if match:
                    security_function_full_number = match.group(1)
                    main_security_function_id = ""
                    component_id = ""

                    if len(security_function_full_number) == 3:
                        main_security_function_id = security_function_full_number[0]
                        component_id = security_function_full_number[1:]
                    elif len(security_function_full_number) == 4:
                        main_security_function_id = security_function_full_number[:2]
                        component_id = security_function_full_number[2:]
                    else:
                        grouped_excel_functions["unknown"]["issues"].append({"key": f"EXCEL-{function_name}", "summary": function_name})
                        continue
                    
                    main_security_function = f"F{main_security_function_id}"
                    
                    dummy_issue = {"key": f"EXCEL-{function_name}", "summary": function_name}
                    
                    grouped_excel_functions[main_security_function]["issues"].append(dummy_issue)
                    grouped_excel_functions[main_security_function]["sub_components"][component_id].append(dummy_issue)
                else:
                    grouped_excel_functions["unknown"]["issues"].append({"key": f"EXCEL-{function_name}", "summary": function_name})
        
        print(f"\nFound {len(grouped_excel_functions)} main security functions (including unknown) in Excel.")
        return grouped_excel_functions

    except Exception as e:
        print(f"An error occurred while reading Excel file: {e}")
        return grouped_excel_functions

def extract_core_sfx_summary(summary):
    """
    Extracts the core security function string (e.g., "F123", "SF4567") from a summary.
    This pattern captures "SF" or "F" followed by digits.
    """
    pattern = re.compile(r'(S?F\d+)')
    match = pattern.search(summary)
    if match:
        return match.group(1)
    return None

def create_jira_issue(jira_connection, summary, issue_type="Requirement Safety SW-Modul", project_key="RBKBK70"):
    """
    Creates a new Jira issue.
    """
    try:
        # Append the required string to the summary
        modified_summary = f"{summary} <Beschreibung> #TODO"
        
        issue_dict = {
            'project': {'key': project_key},
            'summary': modified_summary,
            'issuetype': {'name': issue_type},
        }
        
        new_issue = jira_connection.create_issue(fields=issue_dict)
        print(f"Successfully created Jira issue: {new_issue.key} - {new_issue.fields.summary}")
        return {
            "key": new_issue.key,
            "summary": new_issue.fields.summary,
            "issuetype": new_issue.fields.issuetype.name,
            "issuelinks": [] # New issues have no links initially
        }
    except Exception as e:
        print(f"Error creating Jira issue '{summary}': {e}")
        return None

def create_missing_jira_issues(jira_connection, excel_data, jira_data):
    """
    Compares Excel data with Jira data and creates new Jira issues for entries
    that exist in Excel but not in Jira.
    """
    created_issues = []
    
    # Build a set of normalized Jira summaries for efficient lookup
    normalized_jira_summaries = set()
    for sf_data in jira_data.values():
        for issue in sf_data['issues']:
            core_summary = extract_core_sfx_summary(issue['summary'])
            if core_summary:
                normalized_jira_summaries.add(core_summary)
    
    print("\n--- Identifying and Creating Missing Jira Issues ---")
    for main_sf, sf_data in excel_data.items():
        if main_sf == "unknown":
            continue # Skip unknown category for this process

        for sub_comp_id, excel_issues in sf_data['sub_components'].items():
            for excel_issue in excel_issues:
                excel_original_summary = excel_issue['summary']
                normalized_excel_summary = extract_core_sfx_summary(excel_original_summary)
                
                # Check if an issue with this normalized summary already exists in Jira
                if normalized_excel_summary and normalized_excel_summary not in normalized_jira_summaries:
                    print(f"Excel entry '{excel_original_summary}' (core: '{normalized_excel_summary}') not found in Jira. Creating issue...")
                    new_issue = create_jira_issue(jira_connection, excel_original_summary)
                    if new_issue:
                        created_issues.append(new_issue)
                        # Add the normalized summary of the newly created issue to avoid duplicate creation in the same run
                        new_issue_core_summary = extract_core_sfx_summary(new_issue['summary'])
                        if new_issue_core_summary:
                            normalized_jira_summaries.add(new_issue_core_summary)
                else:
                    print(f"Excel entry '{excel_original_summary}' (core: '{normalized_excel_summary}') already exists in Jira or could not be normalized. Skipping creation.")
    
    return created_issues

def combine_and_display_data(jira_data, excel_data, all_jira_issues_raw):
    """
    Combines and displays security function data from Jira and Excel in a table format.
    Includes all main security functions and sub-components from both datasets.
    Returns the formatted table as a string.
    """
    output_lines = []
    output_lines.append("\n--- Combined Security Function Analysis ---")
    output_lines.append(f"{'Main SF':<10} {'Sub-Component':<20} {'Jira Count':<15} {'Excel Count':<15} {'Linked Sys Req':<15}")
    output_lines.append("-" * 75) # Adjusted for new column

    all_main_sfs = sorted(set(jira_data.keys()) | set(excel_data.keys()))

    # Create a mapping of issue key to full issue data for easy lookup
    all_jira_issues_map = {issue['key']: issue for issue in all_jira_issues_raw}

    # Initialize discrepancy counters
    missing_excel_entries = 0
    missing_jira_links = 0
    missing_jira_link_details = [] # New list to store details of missing links
    missing_jira_elements = 0

    for main_sf in all_main_sfs:
        if main_sf == "unknown":
            continue # Skip unknown category for combined table

        jira_sub_components = jira_data.get(main_sf, {}).get("sub_components", {})
        excel_sub_components = excel_data.get(main_sf, {}).get("sub_components", {})

        all_sub_components = sorted(set(jira_sub_components.keys()) | set(excel_sub_components.keys()))

        if all_sub_components:
            for sub_comp in all_sub_components:
                jira_issues_for_sub_comp = jira_sub_components.get(sub_comp, [])
                jira_count = len(jira_issues_for_sub_comp)
                excel_count = len(excel_sub_components.get(sub_comp, []))
                
                linked_sys_req_count = 0
                for issue in jira_issues_for_sub_comp:
                    full_issue_data = all_jira_issues_map.get(issue['key'])
                    if full_issue_data:
                        current_issue_linked_count = count_linked_system_requirements(full_issue_data)
                        linked_sys_req_count += current_issue_linked_count
                        if current_issue_linked_count == 0: # If an issue has no links
                            missing_jira_link_details.append(issue) # Add the issue to the detailed list
                
                # Check for inconsistency
                if jira_count != excel_count or linked_sys_req_count != jira_count:
                    output_lines.append(f"{main_sf:<10} {sub_comp:<20} {jira_count:<15} {excel_count:<15} {linked_sys_req_count:<15}")
                
                # Update discrepancy counters
                if excel_count == 0 and jira_count > 0:
                    missing_excel_entries += jira_count
                if jira_count == 0 and excel_count > 0:
                    missing_jira_elements += excel_count
                if jira_count > 0 and linked_sys_req_count < jira_count:
                    missing_jira_links += (jira_count - linked_sys_req_count)

        else: # Case where main_sf exists but has no sub-components (e.g., F23 has 1 Jira issue, 0 Excel)
            jira_issues_for_main_sf = jira_data.get(main_sf, {}).get("issues", [])
            jira_count = len(jira_issues_for_main_sf)
            excel_count = len(excel_data.get(main_sf, {}).get("issues", []))
            
            linked_sys_req_count = 0
            for issue in jira_issues_for_main_sf:
                full_issue_data = all_jira_issues_map.get(issue['key'])
                if full_issue_data:
                    current_issue_linked_count = count_linked_system_requirements(full_issue_data)
                    linked_sys_req_count += current_issue_linked_count
                    if current_issue_linked_count == 0: # If an issue has no links
                        missing_jira_link_details.append(issue) # Add the issue to the detailed list

            # Check for inconsistency for main SFs without sub-components
            if (jira_count > 0 or excel_count > 0) and (jira_count != excel_count or linked_sys_req_count != jira_count):
                output_lines.append(f"{main_sf:<10} {'-':<20} {jira_count:<15} {excel_count:<15} {linked_sys_req_count:<15}")
            
            # Update discrepancy counters for main SFs without sub-components
            if excel_count == 0 and jira_count > 0:
                missing_excel_entries += jira_count
            if jira_count == 0 and excel_count > 0:
                missing_jira_elements += excel_count
            if jira_count > 0 and linked_sys_req_count < jira_count:
                missing_jira_links += (jira_count - linked_sys_req_count)
    
    return "\n".join(output_lines), missing_excel_entries, missing_jira_links, missing_jira_elements, missing_jira_link_details

def count_linked_system_requirements(issue):
    """
    Counts how many 'Requirement Safety SW-Modul' issues are linked to 'Requirement Safety SW-System' issues.
    """
    linked_count = 0
    if 'issuelinks' in issue:
        for link in issue['issuelinks']:
            # Debugging: Print linked issue type and key
            print(f"DEBUG: Checking link for {issue['key']}. Linked issue type: {link.get('issuetype')}, Linked issue key: {link.get('key')}")
            if link.get('issuetype') == "Requirement Safety SW-System":
                linked_count += 1
    return linked_count

def update_markdown_doc(table_content, missing_excel_entries, missing_jira_links, missing_jira_elements, missing_jira_link_details):
    """
    Reads the existing Markdown file and replaces the placeholder table with the generated content,
    and appends a summary of discrepancies.
    """
    doc_path = "Sicherheitsfunktionsanalyse.md"
    try:
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Define the placeholder for the table
        placeholder_start = "--- Combined Security Function Analysis ---\nMain SF    Sub-Component        Jira Count      Excel Count     Linked Sys Req \n---------------------------------------------------------------------------"
        
        # Replace the placeholder with the actual table content
        updated_content = content.replace(placeholder_start, table_content)

        # Format missing Jira link details
        missing_links_formatted = ""
        if missing_jira_link_details:
            missing_links_formatted = "\n" + "\n".join([f"    - {issue['key']}: {issue['summary']}" for issue in missing_jira_link_details])
        
        # Append the summary of discrepancies
        summary_section = f"""

## Zusammenfassung der Diskrepanzen

*   **Fehlende Excel-Einträge**: {missing_excel_entries}
*   **Fehlende Links zu Systemanforderungen (Jira Modul Anforderungen)**: {missing_jira_links}
{missing_links_formatted}
*   **Fehlende Modul-Requirements (Excel-Entsprechung ohne Jira Modul Anforderungen)**: {missing_jira_elements}
"""
        # Find the position to insert the summary (after the table)
        # This assumes the table is the last dynamic content before the "Ausführung des Skripts" section
        script_execution_start = "## Ausführung des Skripts"
        if script_execution_start in updated_content:
            updated_content = updated_content.replace(script_execution_start, summary_section + "\n" + script_execution_start)
        else:
            updated_content += summary_section
        
        with open(doc_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print(f"\nUpdated documentation file: {doc_path}")

    except Exception as e:
        print(f"An error occurred while updating the Markdown document: {e}")

def establish_parent_child_relationship(all_jira_issues):
    """
    Establishes parent-child relationships between "Requirement Safety SW-System"
    and "Requirement SW-Modul" issues based on the system function (SFX) extracted from summary.
    Returns a dictionary mapping child (SW-Modul) issue keys to their potential parent (SW-System) issue keys.
    """
    system_requirements_by_sfx = defaultdict(list)
    
    # Pattern to extract the full numeric part after SF or F, accounting for Jira key prefix
    sfx_full_numeric_pattern = re.compile(r'(?:RBKBK70-\d+:\s*)?(?:S?F)(\d+)') 

    # First, build a map of all "Requirement Safety SW-System" issues by their SFX
    # We need to map them by their *target* SF for linking, not just their own SFX
    for issue in all_jira_issues:
        issue_type = issue.get('issuetype', '')
        if issue_type == "Requirement Safety SW-System":
            summary = issue.get('summary', '')
            match = sfx_full_numeric_pattern.search(summary)
            if match:
                sfx_numeric_part = match.group(1)
                # Determine the "main" SF for the system requirement itself
                if len(sfx_numeric_part) == 3:
                    main_sfx_for_system_req = sfx_numeric_part[0]
                elif len(sfx_numeric_part) == 4:
                    main_sfx_for_system_req = sfx_numeric_part[:2]
                else:
                    main_sfx_for_system_req = "unknown" # Should not happen for System Safety SW-System if naming is consistent

                system_requirements_by_sfx[main_sfx_for_system_req].append(issue)
                print(f"DEBUG: Mapped System Req {issue['key']} (SF{sfx_numeric_part}) to main SF {main_sfx_for_system_req}")

    links_to_create = {} # {child_key: parent_key}

    # Now, iterate through "Requirement Safety SW-Modul" issues and find their parents
    for child_issue in all_jira_issues:
        if child_issue.get('issuetype') == "Requirement Safety SW-Modul":
            child_key = child_issue.get('key', 'N/A')
            child_summary = child_issue.get('summary', '')
            
            print(f"DEBUG: Processing Modul Req {child_key} (Summary: '{child_summary}')")
            
            match = sfx_full_numeric_pattern.search(child_summary)
            if not match:
                print(f"DEBUG: No SFX/FXX pattern found for Modul Req {child_key}. Skipping.")
                continue

            child_sfx_numeric_part = match.group(1) # e.g., "012", "1345"
            print(f"DEBUG: Extracted SFX for Modul Req {child_key}: {child_sfx_numeric_part}")

            target_parent_sfx = None

            # Apply specific linking rules
            if child_sfx_numeric_part.startswith("0"):
                target_parent_sfx = "0" # F0XX links with SF0
            elif child_sfx_numeric_part.startswith("13"):
                target_parent_sfx = "1" # F13XX links with SF1
            elif child_sfx_numeric_part.startswith(("15", "16", "17", "20", "21", "29")):
                target_parent_sfx = "5" # F15XX, F16XX, F17XX, F20XX, F21XX, F29XX links with SF5
            elif len(child_sfx_numeric_part) == 3:
                target_parent_sfx = child_sfx_numeric_part[0] # First digit for 3-digit SFX
            elif len(child_sfx_numeric_part) == 4:
                target_parent_sfx = child_sfx_numeric_part[:2] # First two digits for 4-digit SFX
            
            if target_parent_sfx is None:
                print(f"DEBUG: Could not determine target parent SFX for Modul Req {child_key}. Skipping.")
                continue

            potential_parents = system_requirements_by_sfx.get(target_parent_sfx, [])
            
            if potential_parents:
                # Get all keys of potential parent issues for this SFX
                potential_parent_keys = {p['key'] for p in potential_parents}
                
                # Check if link already exists to ANY of the potential parents
                already_linked = False
                if 'issuelinks' in child_issue:
                    print(f"DEBUG: Checking existing links for {child_key}: {child_issue['issuelinks']}")
                    for link in child_issue['issuelinks']:
                        # For "Relates" link type, direction doesn't matter for existence check.
                        # Check if the linked issue's key is one of the potential parents and the type is "Relates".
                        linked_issue_key = link.get("key")
                        linked_issue_type = link.get("type")
                        if linked_issue_type == "Relates" and linked_issue_key in potential_parent_keys:
                            already_linked = True
                            print(f"DEBUG: Link of type 'Relates' already exists for {child_key} to {linked_issue_key}.")
                            break
                else:
                    print(f"DEBUG: No existing issuelinks found for {child_key}.")
                
                if not already_linked:
                    # If no link exists to any of the potential parents, create one to the first one found
                    parent_issue = potential_parents[0] 
                    parent_key = parent_issue['key']
                    links_to_create[child_key] = parent_key
                    print(f"DEBUG: Identified link to create: {child_key} -> {parent_key}")
                else:
                    print(f"DEBUG: Skipping link creation for {child_key} as it already exists to a relevant parent.")
            else:
                print(f"DEBUG: No matching System Requirement found for Modul Req {child_key} (Target SFX: {target_parent_sfx}).")
    
    return links_to_create

if __name__ == "__main__":
    # Fetch both issue types and the Jira connection, forcing a fresh fetch to avoid stale cache issues
    all_jira_issues, jira_connection = get_jira_issues(["Requirement Safety SW-Modul", "Requirement Safety SW-System"], force_fetch=True)
    
    if all_jira_issues and jira_connection:
        excel_raw_functions_data = get_excel_security_functions() # Get raw grouped data from Excel

        # Re-group Jira issues with the new logic (only for "Requirement Safety SW-Modul" for the table)
        jira_grouped_data = defaultdict(lambda: {"issues": [], "sub_components": defaultdict(list)})
        jira_pattern = re.compile(r'(?:RBKBK70-\d+:\s*)?(?:S?F)(\d+)') 
        for issue in all_jira_issues:
            if issue.get('issuetype') == "Requirement Safety SW-Modul": # Only process SW-Modul for this grouping
                summary = issue.get('summary', '')
                match = jira_pattern.search(summary)
                if match:
                    security_function_full_number = match.group(1)
                    main_security_function_id = ""
                    component_id = ""

                    if len(security_function_full_number) == 3:
                        main_security_function_id = security_function_full_number[0]
                        component_id = security_function_full_number[1:]
                    elif len(security_function_full_number) == 4:
                        main_security_function_id = security_function_full_number[:2]
                        component_id = security_function_full_number[2:]
                    else:
                        jira_grouped_data["unknown"]["issues"].append(issue)
                        continue
                    
                    main_security_function = f"F{main_security_function_id}"
                    jira_grouped_data[main_security_function]["issues"].append(issue)
                    jira_grouped_data[main_security_function]["sub_components"][component_id].append(issue)
                else:
                    jira_grouped_data["unknown"]["issues"].append(issue)

        # Create missing Jira issues
        newly_created_issues = create_missing_jira_issues(jira_connection, excel_raw_functions_data, jira_grouped_data)
        
        # Add newly created issues to the all_jira_issues list for subsequent linking and analysis
        all_jira_issues.extend(newly_created_issues)

        # Re-group Jira issues including the newly created ones for accurate linking and display
        jira_grouped_data_after_creation = defaultdict(lambda: {"issues": [], "sub_components": defaultdict(list)})
        for issue in all_jira_issues:
            if issue.get('issuetype') == "Requirement Safety SW-Modul":
                summary = issue.get('summary', '')
                match = jira_pattern.search(summary)
                if match:
                    security_function_full_number = match.group(1)
                    main_security_function_id = ""
                    component_id = ""

                    if len(security_function_full_number) == 3:
                        main_security_function_id = security_function_full_number[0]
                        component_id = security_function_full_number[1:]
                    elif len(security_function_full_number) == 4:
                        main_security_function_id = security_function_full_number[:2]
                        component_id = security_function_full_number[2:]
                    else:
                        jira_grouped_data_after_creation["unknown"]["issues"].append(issue)
                        continue
                    
                    main_security_function = f"F{main_security_function_id}"
                    jira_grouped_data_after_creation[main_security_function]["issues"].append(issue)
                    jira_grouped_data_after_creation[main_security_function]["sub_components"][component_id].append(issue)
                else:
                    jira_grouped_data_after_creation["unknown"]["issues"].append(issue)

        links_to_create = establish_parent_child_relationship(all_jira_issues)

        print("\n--- Creating Missing Jira Links ---")
        if links_to_create:
            for child_key, parent_key in links_to_create.items():
                create_jira_issue_link(
                    jira_connection,
                    inward_key=child_key,
                    outward_key=parent_key,
                    link_type="Relates"
                )
        else:
            print("No new links identified to create.")
    
        if all_jira_issues and excel_raw_functions_data:
            combined_table_output, missing_excel_entries, missing_jira_links, missing_jira_elements, missing_jira_link_details = combine_and_display_data(jira_grouped_data_after_creation, excel_raw_functions_data, all_jira_issues)
            print(combined_table_output) # Print to console as well
            update_markdown_doc(combined_table_output, missing_excel_entries, missing_jira_links, missing_jira_elements, missing_jira_link_details)
        else:
            print("Could not retrieve data from both Jira and Excel for comparison.")
            if all_jira_issues:
                print("\n--- Jira Requirements (Grouped) ---")
                for sf, data in sorted(jira_grouped_data_after_creation.items()):
                    reqs = data["issues"]
                    sub_components_dict = data["sub_components"]
                    print(f"Security Function {sf}: {len(reqs)} requirements")
                    if sf != "unknown":
                        sorted_sub_components_keys = sorted(sub_components_dict.keys())
                        sub_component_strings = []
                        for sub_comp_key in sorted_sub_components_keys:
                            sub_component_strings.append(f"{sub_comp_key} ({len(sub_components_dict[sub_comp_key])})")
                        print(f"  Sub-components: {', '.join(sub_component_strings)}")
            if excel_raw_functions_data:
                print("\n--- Security Functions from Excel (Grouped) ---")
                for sf, data in sorted(excel_raw_functions_data.items()):
                    reqs = data["issues"]
                    sub_components_dict = data["sub_components"]
                    print(f"Security Function {sf}: {len(reqs)} requirements")
                    if sf != "unknown":
                        sorted_sub_components_keys = sorted(sub_components_dict.keys())
                        sub_component_strings = []
                        for sub_comp_key in sorted_sub_components_keys:
                            sub_component_strings.append(f"{sub_comp_key} ({len(sub_components_dict[sub_comp_key])})")
                        print(f"  Sub-components: {', '.join(sub_component_strings)}")
